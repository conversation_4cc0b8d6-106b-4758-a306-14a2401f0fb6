{"nap_server_address": {"description": "NAP cat 服务地址,若与服务器在同一服务器上请填写localhost", "type": "string", "default": "localhost"}, "nap_server_port": {"description": "NAP cat 所处服务器接收文件端口，在同一服务器上可以不填", "type": "int", "default": 3658}, "douyin_api_url": {"description": "抖音API地址", "type": "string", "default": "https://douyin.wtf/api/hybrid/video_data", "hint": "如需部署私有解析服务以提高解析速度请访问此库：https://github.com/Evil0ctal/Do<PERSON>in_TikTok_Download_API"}, "delate_time": {"description": "删除文件时间（单位分钟）", "type": "int", "default": 60}, "max_video_size": {"description": "最大视频大小（单位MB）（预留，还未完全实现）", "type": "int", "default": 200}, "bili_quality": {"description": "B站视频清晰度", "type": "int", "default": 32, "hint": "16: 360P|32: 480P|64: 720P|80: 1080P|112: 1080P+（高码率）|120: 4K"}, "bili_reply_mode": {"description": "回复模式", "type": "int", "default": 3, "hint": "0: 纯文本回复|1: 图片|2: 视频|3: 图片+视频|4: 纯视频回复"}, "bili_url_mode": {"description": "是否生成直链", "type": "bool", "default": true, "hint": "是否在哔哩哔哩视频解析时生成直链，默认生成直链"}, "Merge_and_forward": {"description": "是否合并转发", "type": "bool", "default": false, "hint": "是否在转发视频时合并视频和图片，默认不合并"}, "bili_use_login": {"description": "是否使用登录状态下载B站视频", "hint": "启用后将尝试使用登录状态下载高清视频，禁用则强制使用无登录方式", "type": "bool", "default": false}, "xhs_reply_mode": {"description": "小红书是否开启合并转发", "type": "bool", "default": true, "hint": "默认合并"}}